import { Controller, Get, Query } from '@nestjs/common';
import { AnalyticsService } from './analytics.service';

@Controller('admin/analytics')
export class AnalyticsController {
  constructor(private readonly service: AnalyticsService) {}

  @Get('getAprWiseAnalytics')
  async getAprWiseAnalytics(@Query() query) {
    return await this.service.getAprWiseAnalytics(query);
  }
  @Get('funEmiRepaidCount')
  async funEmiRepaidCount(@Query() query) {
    return await this.service.funEmiRepaidCount(query?.month);
  }
}
